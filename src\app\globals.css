@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:where(.dark, .dark *));

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: Inter, sans-serif;
  --font-mono: JetBrains Mono, monospace;
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --font-serif: Georgia, serif;
  --radius: 0.5rem;
  --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
  --tracking-tight: calc(var(--tracking-normal) - 0.025em);
  --tracking-wide: calc(var(--tracking-normal) + 0.025em);
  --tracking-wider: calc(var(--tracking-normal) + 0.05em);
  --tracking-widest: calc(var(--tracking-normal) + 0.1em);
  --tracking-normal: var(--tracking-normal);
  --shadow-2xl: var(--shadow-2xl);
  --shadow-xl: var(--shadow-xl);
  --shadow-lg: var(--shadow-lg);
  --shadow-md: var(--shadow-md);
  --shadow: var(--shadow);
  --shadow-sm: var(--shadow-sm);
  --shadow-xs: var(--shadow-xs);
  --shadow-2xs: var(--shadow-2xs);
  --spacing: var(--spacing);
  --letter-spacing: var(--letter-spacing);
  --shadow-offset-y: var(--shadow-offset-y);
  --shadow-offset-x: var(--shadow-offset-x);
  --shadow-spread: var(--shadow-spread);
  --shadow-blur: var(--shadow-blur);
  --shadow-opacity: var(--shadow-opacity);
  --color-shadow-color: var(--shadow-color);
  --color-destructive-foreground: var(--destructive-foreground);
}

:root {
  --radius: 0.5rem;
  --background: oklch(0.9730 0.0133 286.1503);
  --foreground: oklch(0.3015 0.0572 282.4176);
  --card: oklch(1.0000 0 0);
  --card-foreground: oklch(0.3015 0.0572 282.4176);
  --popover: oklch(1.0000 0 0);
  --popover-foreground: oklch(0.3015 0.0572 282.4176);
  --primary: oklch(0.5417 0.1790 288.0332);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.9174 0.0435 292.6901);
  --secondary-foreground: oklch(0.4143 0.1039 288.1742);
  --muted: oklch(0.9580 0.0133 286.1454);
  --muted-foreground: oklch(0.5426 0.0465 284.7435);
  --accent: oklch(0.9221 0.0373 262.1410);
  --accent-foreground: oklch(0.3015 0.0572 282.4176);
  --destructive: oklch(0.6861 0.2061 14.9941);
  --border: oklch(0.9115 0.0216 285.9625);
  --input: oklch(0.9115 0.0216 285.9625);
  --ring: oklch(0.5417 0.1790 288.0332);
  --chart-1: oklch(0.5417 0.1790 288.0332);
  --chart-2: oklch(0.7042 0.1602 288.9880);
  --chart-3: oklch(0.5679 0.2113 276.7065);
  --chart-4: oklch(0.6356 0.1922 281.8054);
  --chart-5: oklch(0.4509 0.1758 279.3838);
  --sidebar: oklch(0.9580 0.0133 286.1454);
  --sidebar-foreground: oklch(0.3015 0.0572 282.4176);
  --sidebar-primary: oklch(0.5417 0.1790 288.0332);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.9221 0.0373 262.1410);
  --sidebar-accent-foreground: oklch(0.3015 0.0572 282.4176);
  --sidebar-border: oklch(0.9115 0.0216 285.9625);
  --sidebar-ring: oklch(0.5417 0.1790 288.0332);
  --destructive-foreground: oklch(1.0000 0 0);
  --font-sans: Inter, sans-serif;
  --font-serif: Georgia, serif;
  --font-mono: JetBrains Mono, monospace;
  --shadow-color: hsl(240 30% 25%);
  --shadow-opacity: 0.12;
  --shadow-blur: 10px;
  --shadow-spread: 0px;
  --shadow-offset-x: 0px;
  --shadow-offset-y: 4px;
  --letter-spacing: 0em;
  --spacing: 0.25rem;
  --shadow-2xs: 0px 4px 10px 0px hsl(240 30% 25% / 0.06);
  --shadow-xs: 0px 4px 10px 0px hsl(240 30% 25% / 0.06);
  --shadow-sm: 0px 4px 10px 0px hsl(240 30% 25% / 0.12), 0px 1px 2px -1px hsl(240 30% 25% / 0.12);
  --shadow: 0px 4px 10px 0px hsl(240 30% 25% / 0.12), 0px 1px 2px -1px hsl(240 30% 25% / 0.12);
  --shadow-md: 0px 4px 10px 0px hsl(240 30% 25% / 0.12), 0px 2px 4px -1px hsl(240 30% 25% / 0.12);
  --shadow-lg: 0px 4px 10px 0px hsl(240 30% 25% / 0.12), 0px 4px 6px -1px hsl(240 30% 25% / 0.12);
  --shadow-xl: 0px 4px 10px 0px hsl(240 30% 25% / 0.12), 0px 8px 10px -1px hsl(240 30% 25% / 0.12);
  --shadow-2xl: 0px 4px 10px 0px hsl(240 30% 25% / 0.30);
  --tracking-normal: 0em;
}

.dark {
  --background: oklch(0.1743 0.0227 283.7998);
  --foreground: oklch(0.9185 0.0257 285.8834);
  --card: oklch(0.2284 0.0384 282.9324);
  --card-foreground: oklch(0.9185 0.0257 285.8834);
  --popover: oklch(0.2284 0.0384 282.9324);
  --popover-foreground: oklch(0.9185 0.0257 285.8834);
  --primary: oklch(0.7162 0.1597 290.3962);
  --primary-foreground: oklch(0.1743 0.0227 283.7998);
  --secondary: oklch(0.3139 0.0736 283.4591);
  --secondary-foreground: oklch(0.8367 0.0849 285.9111);
  --muted: oklch(0.2710 0.0621 281.4377);
  --muted-foreground: oklch(0.7166 0.0462 285.1741);
  --accent: oklch(0.3354 0.0828 280.9705);
  --accent-foreground: oklch(0.9185 0.0257 285.8834);
  --destructive: oklch(0.6861 0.2061 14.9941);
  --border: oklch(0.3261 0.0597 282.5832);
  --input: oklch(0.3261 0.0597 282.5832);
  --ring: oklch(0.7162 0.1597 290.3962);
  --chart-1: oklch(0.7162 0.1597 290.3962);
  --chart-2: oklch(0.6382 0.1047 274.9117);
  --chart-3: oklch(0.7482 0.1235 244.7492);
  --chart-4: oklch(0.7124 0.0977 186.6761);
  --chart-5: oklch(0.7546 0.1831 346.8124);
  --sidebar: oklch(0.2284 0.0384 282.9324);
  --sidebar-foreground: oklch(0.9185 0.0257 285.8834);
  --sidebar-primary: oklch(0.7162 0.1597 290.3962);
  --sidebar-primary-foreground: oklch(0.1743 0.0227 283.7998);
  --sidebar-accent: oklch(0.3354 0.0828 280.9705);
  --sidebar-accent-foreground: oklch(0.9185 0.0257 285.8834);
  --sidebar-border: oklch(0.3261 0.0597 282.5832);
  --sidebar-ring: oklch(0.7162 0.1597 290.3962);
  --destructive-foreground: oklch(1.0000 0 0);
  --radius: 0.5rem;
  --font-sans: Inter, sans-serif;
  --font-serif: Georgia, serif;
  --font-mono: JetBrains Mono, monospace;
  --shadow-color: hsl(240 30% 25%);
  --shadow-opacity: 0.12;
  --shadow-blur: 10px;
  --shadow-spread: 0px;
  --shadow-offset-x: 0px;
  --shadow-offset-y: 4px;
  --letter-spacing: 0em;
  --spacing: 0.25rem;
  --shadow-2xs: 0px 4px 10px 0px hsl(240 30% 25% / 0.06);
  --shadow-xs: 0px 4px 10px 0px hsl(240 30% 25% / 0.06);
  --shadow-sm: 0px 4px 10px 0px hsl(240 30% 25% / 0.12), 0px 1px 2px -1px hsl(240 30% 25% / 0.12);
  --shadow: 0px 4px 10px 0px hsl(240 30% 25% / 0.12), 0px 1px 2px -1px hsl(240 30% 25% / 0.12);
  --shadow-md: 0px 4px 10px 0px hsl(240 30% 25% / 0.12), 0px 2px 4px -1px hsl(240 30% 25% / 0.12);
  --shadow-lg: 0px 4px 10px 0px hsl(240 30% 25% / 0.12), 0px 4px 6px -1px hsl(240 30% 25% / 0.12);
  --shadow-xl: 0px 4px 10px 0px hsl(240 30% 25% / 0.12), 0px 8px 10px -1px hsl(240 30% 25% / 0.12);
  --shadow-2xl: 0px 4px 10px 0px hsl(240 30% 25% / 0.30);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    letter-spacing: var(--tracking-normal);
  }
}