"use client"
import React, { useState, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { EditIcon, PlusIcon, SparklesIcon } from 'lucide-react';
import { toast } from 'sonner';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface CustomModelCardProps {
  providers: Array<{
    id: string;
    name: string;
    models: Array<{
      id: string;
      name: string;
      description?: string;
      strength?: string;
      enabled?: boolean;
      custom?: boolean;
    }>;
  }>;
  onAddModel: (providerId: string, model: any) => boolean;
}

const generateNameFromId = (id: string, providerId: string, providerName: string) => {
  if (!id || !providerId || !providerName) return '';

  const modelPart = id.toLowerCase().startsWith(providerId.toLowerCase())
    ? id.slice(providerId.length)
    : id;

  const formattedModelPart = modelPart
    .replace(/^[_-]/, '')
    .split(/[-_]/)
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');

  return `${providerName} ${formattedModelPart}`.trim();
};

export const CustomModelCard: React.FC<CustomModelCardProps> = ({ providers, onAddModel }) => {
  const [selectedProvider, setSelectedProvider] = useState('');
  const [isNameEditing, setIsNameEditing] = useState(false);
  const [newModel, setNewModel] = useState({
    id: '',
    name: '',
    description: '',
    strength: ''
  });

  const handleModelIdChange = (id: string) => {
    const inferredProvider = providers.find(p => id.toLowerCase().startsWith(p.id.toLowerCase()));
    const providerId = inferredProvider ? inferredProvider.id : '';
    const providerName = inferredProvider ? inferredProvider.name : '';
    
    setSelectedProvider(providerId);
    
    const modelName = generateNameFromId(id, providerId, providerName);

    setNewModel(prev => ({
      ...prev,
      id,
      name: isNameEditing ? prev.name : modelName,
    }));
  };

  const handleModelNameChange = (name: string) => {
    if (!isNameEditing) {
      setIsNameEditing(true);
    }
    setNewModel(prev => ({ ...prev, name }));
  }

  const handleAddModel = () => {
    if (!selectedProvider) {
      toast.error('Could not determine a provider. Please select one manually.');
      return;
    }
    
    if (!newModel.id || !newModel.name) {
      toast.error('Model ID and Name are required.');
      return;
    }
    
    const provider = providers.find(p => p.id === selectedProvider);
    if (provider && provider.models.some(model => model.id === newModel.id)) {
      toast.error(`Model with ID "${newModel.id}" already exists for ${provider.name}.`);
      return;
    }

    const success = onAddModel(selectedProvider, {
      ...newModel,
      custom: true,
      enabled: true
    });
    
    if (success) {
      toast.success(`Model "${newModel.name}" added successfully!`);
      
      setNewModel({ id: '', name: '', description: '', strength: '' });
      setSelectedProvider('');
      setIsNameEditing(false);
    }
  };

  const generatedName = useMemo(() => {
    const provider = providers.find(p => p.id === selectedProvider);
    return generateNameFromId(newModel.id, selectedProvider, provider?.name || '');
  }, [newModel.id, selectedProvider, providers]);

  return (
    <Card className="flex-1 h-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <SparklesIcon className="h-5 w-5" />
          Add Custom AI Model
        </CardTitle>
        <CardDescription>
          Configure and add a new AI model from any provider by its ID.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        
        <div>
          <Label htmlFor="model-id">Model ID</Label>
          <p className="text-xs text-muted-foreground mt-1 mb-2">
            The official ID of the model (e.g., gemini-1.5-pro-latest).
          </p>
          <Input
            id="model-id"
            value={newModel.id}
            onChange={(e) => handleModelIdChange(e.target.value)}
            placeholder="Enter model ID here..."
            className="w-full"
          />
        </div>

        <div>
          <Label>Provider (Auto-Detected)</Label>
           <p className="text-xs text-muted-foreground mt-1 mb-2">
            The provider is detected from the Model ID. You can change it if needed.
          </p>
          <Select
            value={selectedProvider}
            onValueChange={setSelectedProvider}
            disabled={!newModel.id}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select a provider" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                {providers.map((provider) => (
                  <SelectItem key={provider.id} value={provider.id}>
                    {provider.name}
                  </SelectItem>
                ))}
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>

        <div>
          <div className="flex items-center justify-between">
            <Label htmlFor="model-name">Model Name</Label>
            {!isNameEditing && (
              <Button variant="ghost" size="sm" onClick={() => setIsNameEditing(true)} disabled={!newModel.id}>
                <EditIcon className="h-3 w-3 mr-1"/>
                Edit
              </Button>
            )}
          </div>
           <p className="text-xs text-muted-foreground mt-1 mb-2">
            A user-friendly name for the model (auto-generated from ID).
          </p>
          {isNameEditing ? (
            <Input
              id="model-name"
              value={newModel.name}
              onChange={(e) => handleModelNameChange(e.target.value)}
              placeholder="e.g., Custom GPT-4"
              className="w-full"
              disabled={!newModel.id}
            />
          ) : (
            <p className="text-sm text-muted-foreground p-2 h-9 bg-muted rounded-md flex items-center">
              {newModel.name || "(auto-generated)"}
            </p>
          )}
        </div>

        <div>
          <Label htmlFor="model-description">Description (Optional)</Label>
           <p className="text-xs text-muted-foreground mt-1 mb-2">
            A brief description of what this model is or does.
          </p>
          <Input
            id="model-description"
            value={newModel.description}
            onChange={(e) => setNewModel(prev => ({...prev, description: e.target.value}))}
            placeholder="e.g., My custom fine-tuned model"
            className="w-full"
          />
        </div>

        <div>
          <Label htmlFor="model-strength">Strength / ELO (Optional)</Label>
          <p className="text-xs text-muted-foreground mt-1 mb-2">
            An estimated ELO rating or strength level for sorting.
          </p>
          <Input
            id="model-strength"
            value={newModel.strength}
            onChange={(e) => setNewModel(prev => ({...prev, strength: e.target.value}))}
            placeholder="e.g., 2850"
            className="w-full"
          />
        </div>

        <Button onClick={handleAddModel} className="w-full" disabled={!newModel.id || !selectedProvider}>
          <PlusIcon className="h-4 w-4 mr-2" />
          Add Model to Provider
        </Button>
      </CardContent>
    </Card>
  );
};